package v1

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestGenerateCacheKey(t *testing.T) {
	// Test cache key generation
	req1 := countryReq{
		QueryFields: []string{"name"},
		QueryText:   "vietnam",
	}

	req2 := countryReq{
		QueryFields: []string{"name"},
		QueryText:   "vietnam",
	}

	req3 := countryReq{
		QueryFields: []string{"name"},
		QueryText:   "thailand",
	}

	key1 := generateCacheKey("test", req1)
	key2 := generateCacheKey("test", req2)
	key3 := generateCacheKey("test", req3)

	// Same requests should generate same cache keys
	assert.Equal(t, key1, key2, "Same requests should generate same cache keys")

	// Different requests should generate different cache keys
	assert.NotEqual(t, key1, key3, "Different requests should generate different cache keys")

	// Cache keys should have the correct prefix
	assert.Contains(t, key1, "test:", "Cache key should contain the prefix")
}

func TestCacheKeyUniqueness(t *testing.T) {
	// Test that different request types generate different cache keys
	countryReq := countryReq{
		QueryFields: []string{"name"},
		QueryText:   "test",
	}

	airlineReq := airlineReq{
		QueryFields: []string{"name"},
		QueryText:   "test",
		Limit:       100,
		Offset:      0,
	}

	airportReq := AirportReq{
		QueryFields: []string{"name"},
		QueryText:   "test",
		Limit:       100,
		Offset:      0,
	}

	countryKey := generateCacheKey("country", countryReq)
	airlineKey := generateCacheKey("airline", airlineReq)
	airportKey := generateCacheKey("airport", airportReq)

	// All keys should be different
	assert.NotEqual(t, countryKey, airlineKey, "Country and airline cache keys should be different")
	assert.NotEqual(t, countryKey, airportKey, "Country and airport cache keys should be different")
	assert.NotEqual(t, airlineKey, airportKey, "Airline and airport cache keys should be different")
}

func TestCacheKeyWithDifferentParameters(t *testing.T) {
	// Test that different parameters generate different cache keys
	req1 := AirportReq{
		QueryFields: []string{"name"},
		QueryText:   "test",
		Limit:       100,
		Offset:      0,
	}

	req2 := AirportReq{
		QueryFields: []string{"name"},
		QueryText:   "test",
		Limit:       200, // Different limit
		Offset:      0,
	}

	req3 := AirportReq{
		QueryFields: []string{"name"},
		QueryText:   "test",
		Limit:       100,
		Offset:      10, // Different offset
	}

	req4 := AirportReq{
		QueryFields: []string{"iata"}, // Different query fields
		QueryText:   "test",
		Limit:       100,
		Offset:      0,
	}

	key1 := generateCacheKey("airport", req1)
	key2 := generateCacheKey("airport", req2)
	key3 := generateCacheKey("airport", req3)
	key4 := generateCacheKey("airport", req4)

	// All keys should be different
	assert.NotEqual(t, key1, key2, "Different limits should generate different cache keys")
	assert.NotEqual(t, key1, key3, "Different offsets should generate different cache keys")
	assert.NotEqual(t, key1, key4, "Different query fields should generate different cache keys")
}

// Mock test for cache functionality (requires Redis setup for full integration test)
func TestCacheFunctionality(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create a test router
	router := gin.New()

	// Mock the Redis middleware (in real test, you'd set up a test Redis instance)
	router.Use(func(c *gin.Context) {
		// Mock Redis client - in real test you'd use a test Redis instance
		c.Set("redis", nil) // This will make cache functions return early
		c.Next()
	})

	// Test that the handler works without cache (when Redis is not available)
	router.GET("/test/countries", GetCountryList)

	// Create a test request
	req, _ := http.NewRequest("GET", "/test/countries?query_text=test", nil)
	w := httptest.NewRecorder()

	// This test mainly verifies that the handler doesn't crash when Redis is not available
	router.ServeHTTP(w, req)

	// The handler should still work (though it will fail due to missing DAO)
	// In a real integration test, you'd set up the full middleware stack
	assert.NotEqual(t, http.StatusOK, w.Code, "Handler should handle missing dependencies gracefully")
}

func TestCacheKeyConsistency(t *testing.T) {
	// Test that the same request parameters always generate the same cache key
	req := airlineReq{
		QueryFields: []string{"name", "icao"},
		QueryText:   "vietnam airlines",
		Limit:       50,
		Offset:      10,
	}

	// Generate the same cache key multiple times
	keys := make([]string, 10)
	for i := 0; i < 10; i++ {
		keys[i] = generateCacheKey("airline_list", req)
	}

	// All keys should be identical
	for i := 1; i < len(keys); i++ {
		assert.Equal(t, keys[0], keys[i], "Cache key generation should be consistent")
	}
}
