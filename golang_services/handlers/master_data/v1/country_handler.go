package v1

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"
	"gorm.io/datatypes"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
	"bitbucket.org/persistence17/aria/golang_services/sdk/db"
)

type countryReq struct {
	QueryFields []string `form:"query_fields"`
	QueryText   string   `form:"query_text"`
}

type countryResponse struct {
	Data    []datatypes.JSON `json:"data"`
	Limit   uint64           `json:"limit"`
	Offset  uint64           `json:"offset"`
	Total   uint64           `json:"total"`
	Success bool             `json:"success"`
}

// GetCountryList get country list
func GetCountryList(c *gin.Context) {
	var (
		req countryReq
		err error
	)

	if err = c.ShouldBind<PERSON>uery(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get cached or fresh master data
	airportsRaw := getCachedMasterData(c, dao, "country")

	countries := []models.Country{}
	json.Unmarshal(airportsRaw, &countries)

	if len(req.QueryFields) == 0 {
		req.QueryFields = []string{"name"}
	}

	countries = funk.Filter(countries, func(country models.Country) bool {
		if req.QueryText == "" {
			return true
		}
		if funk.ContainsString(req.QueryFields, "name") && strings.Contains(strings.ToLower(country.Name), strings.ToLower(req.QueryText)) {
			return true
		}
		if strings.Contains(strings.ToLower(country.IsoAlpha3), strings.ToLower(req.QueryText)) {
			return true
		}
		return false
	}).([]models.Country)

	offset := 0
	limit := 500

	if offset < 0 {
		offset = 0
	}
	if limit < 0 {
		limit = 0
	}
	if offset > len(countries) {
		offset = len(countries)
	}
	if offset+limit > len(countries) {
		limit = len(countries) - offset
	}

	// Perform the slicing
	countries = countries[offset : offset+limit]

	c.JSON(http.StatusOK, gin.H{
		"data":    countries,
		"success": true,
	})
}

type airlineReq struct {
	QueryFields []string `form:"query_fields"`
	QueryText   string   `form:"query_text"`
	Limit       int      `form:"limit"`
	Offset      int      `form:"offset"`
}

type airlineResponse struct {
	Data    []datatypes.JSON `json:"data"`
	Limit   uint64           `json:"limit"`
	Offset  uint64           `json:"offset"`
	Total   uint64           `json:"total"`
	Success bool             `json:"success"`
}

// GetAirlineList get airline list
func GetAirlineList(c *gin.Context) {
	var (
		req airlineReq
		err error
	)

	if err = c.ShouldBindQuery(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if req.Limit <= 0 {
		req.Limit = 200
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get cached or fresh master data
	airportsRaw := getCachedMasterData(c, dao, "airline")

	airlines := []models.Airline{}
	json.Unmarshal(airportsRaw, &airlines)

	if len(req.QueryFields) == 0 {
		req.QueryFields = []string{"name", "icao"}
	}

	if req.QueryText != "" {
		airlines = funk.Filter(airlines, func(airport models.Airline) bool {
			if funk.ContainsString(req.QueryFields, "name") && strings.Contains(strings.ToLower(airport.Name), strings.ToLower(req.QueryText)) {
				return true
			} else if funk.ContainsString(req.QueryFields, "icao") && strings.Contains(strings.ToLower(airport.Icao), strings.ToLower(req.QueryText)) {
				return true
			}
			return false
		}).([]models.Airline)

		sortAirlines(airlines, req.QueryText)
	}
	offset := req.Offset
	limit := req.Limit

	if offset < 0 {
		offset = 0
	}
	if limit < 0 {
		limit = 0
	}
	if offset > len(airlines) {
		offset = len(airlines)
	}
	if offset+limit > len(airlines) {
		limit = len(airlines) - offset
	}

	// Perform the slicing
	airlines = airlines[offset : offset+limit]

	c.JSON(http.StatusOK, gin.H{
		"data":    airlines,
		"success": true,
	})
}

type AirportReq struct {
	QueryFields []string `form:"query_fields"`
	QueryText   string   `form:"query_text"`
	Iata        []string `form:"iata"`
	CountryCode []string `form:"country_code"`
	Limit       int      `form:"limit"`
	Offset      int      `form:"offset"`
}

type AirportResponse struct {
	Data    any   `json:"data"`
	Limit   int   `json:"limit"`
	Offset  int   `json:"offset"`
	Total   int64 `json:"total"`
	Success bool  `json:"success"`
}

// GetAirportList get airport list
func GetAirportList(c *gin.Context) {
	var req AirportReq
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if req.Limit <= 0 {
		req.Limit = 100
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get cached or fresh master data
	airportsRaw := getCachedMasterData(c, dao, "airport")

	airports := []models.Airport{}
	json.Unmarshal(airportsRaw, &airports)
	compareString := func(list []string, item string) bool {
		if item == "" {
			return false
		}
		for _, i := range list {
			if strings.Contains(strings.ToLower(i), strings.ToLower(item)) {
				return true
			}
		}
		return false
	}

	if len(req.Iata) > 0 {
		airports = funk.Filter(airports, func(airport models.Airport) bool {
			return compareString(req.Iata, airport.Iata)
		}).([]models.Airport)
	}

	if len(req.CountryCode) > 0 {
		airports = funk.Filter(airports, func(airport models.Airport) bool {
			return compareString(req.CountryCode, airport.CountryCode)
		}).([]models.Airport)
	}

	if len(req.QueryFields) > 0 && req.QueryText != "" {
		airports = funk.Filter(airports, func(airport models.Airport) bool {
			return (funk.ContainsString(req.QueryFields, "iata") && compareString([]string{airport.Iata}, req.QueryText)) ||
				(funk.ContainsString(req.QueryFields, "name") && compareString([]string{airport.Name}, req.QueryText)) ||
				(funk.ContainsString(req.QueryFields, "city") && compareString([]string{airport.City}, req.QueryText)) ||
				(funk.ContainsString(req.QueryFields, "country_code") && compareString([]string{airport.CountryCode}, req.QueryText))
		}).([]models.Airport)

		sortAirports(airports, req.QueryText)

	}

	offset := req.Offset
	limit := req.Limit

	if offset < 0 {
		offset = 0
	}
	if limit < 0 {
		limit = 0
	}
	if offset > len(airports) {
		offset = len(airports)
	}
	if offset+limit > len(airports) {
		limit = len(airports) - offset
	}

	// Perform the slicing
	airports = airports[offset : offset+limit]

	response := AirportResponse{
		Data:    airports,
		Limit:   req.Limit,
		Offset:  req.Offset,
		Success: true,
	}

	c.JSON(http.StatusOK, response)
}

func sortAirports(airports []models.Airport, queryText string) {
	sort.Slice(airports, func(i, j int) bool {
		containsI := strings.Contains(strings.ToLower(airports[i].Iata), strings.ToLower(queryText))
		containsJ := strings.Contains(strings.ToLower(airports[j].Iata), strings.ToLower(queryText))

		if containsI && containsJ {
			return airports[i].Name < airports[j].Name
		}

		if containsI {
			return true
		}

		if containsJ {
			return false
		}

		return airports[i].Name < airports[j].Name
	})
}

func sortAirlines(airlines []models.Airline, queryText string) {
	sort.Slice(airlines, func(i, j int) bool {
		containsI := strings.Contains(strings.ToLower(airlines[i].Iata), strings.ToLower(queryText))
		containsJ := strings.Contains(strings.ToLower(airlines[j].Iata), strings.ToLower(queryText))

		if containsI && containsJ {
			return airlines[i].Name < airlines[j].Name
		}

		if containsI {
			return true
		}

		if containsJ {
			return false
		}

		return airlines[i].Name < airlines[j].Name
	})
}

// Cache utility functions
const (
	masterDataCacheTTL = 3600 // 1 hour in seconds for master data
)

// getCachedMasterData retrieves cached master data or fetches from database if not cached
func getCachedMasterData(c *gin.Context, dao db.IDao, dataName string) []byte {
	// Generate cache key for master data
	cacheKey := fmt.Sprintf("master_data:%s", dataName)

	// Try to get from Redis cache first
	rdb := middlewares.GetRedis(c)
	if rdb != nil {
		// Set timeout for Redis GET operation to avoid blocking
		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		if cachedData, err := rdb.Get(ctx, cacheKey).Result(); err == nil {
			// Return cached data as bytes
			return []byte(cachedData)
		}
		// If Redis get fails (timeout, connection error, etc.), continue to database
		// Don't log here as it's expected behavior when cache is empty or Redis is slow
	}

	// Cache miss or Redis unavailable - fetch from database
	rawData, _ := dao.DB().GetMasterDataValueByName(dataName)

	// Store in cache for next time (if Redis is available and data is valid)
	if rdb != nil && len(rawData) > 0 {
		// Use a separate goroutine for cache write to avoid blocking the response
		go func() {
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			if err := rdb.Set(ctx, cacheKey, string(rawData), time.Duration(masterDataCacheTTL)*time.Second).Err(); err != nil {
				// Log error but don't fail the request
				log.Printf("Warning: Failed to cache master data for %s: %v", dataName, err)
			}
		}()
	}

	return rawData
}
