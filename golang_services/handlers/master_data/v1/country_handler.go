package v1

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"
	"gorm.io/datatypes"

	"bitbucket.org/persistence17/aria/golang_services/middlewares"
	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/response"
)

type countryReq struct {
	QueryFields []string `form:"query_fields"`
	QueryText   string   `form:"query_text"`
}

type countryResponse struct {
	Data    []datatypes.JSON `json:"data"`
	Limit   uint64           `json:"limit"`
	Offset  uint64           `json:"offset"`
	Total   uint64           `json:"total"`
	Success bool             `json:"success"`
}

// GetCountryList get country list
func GetCountryList(c *gin.Context) {
	var (
		req countryReq
		err error
	)

	if err = c.Should<PERSON>ind<PERSON>uery(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}

	// Generate cache key based on request parameters
	cacheKey := generateCacheKey("country_list", req)

	// Try to get cached response
	if cachedData, found := getCachedResponse(c, cacheKey); found {
		c.JSON(http.StatusOK, cachedData)
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	airportsRaw, _ := dao.DB().GetMasterDataValueByName("country")

	countries := []models.Country{}
	json.Unmarshal(airportsRaw, &countries)

	if len(req.QueryFields) == 0 {
		req.QueryFields = []string{"name"}
	}

	countries = funk.Filter(countries, func(country models.Country) bool {
		if req.QueryText == "" {
			return true
		}
		if funk.ContainsString(req.QueryFields, "name") && strings.Contains(strings.ToLower(country.Name), strings.ToLower(req.QueryText)) {
			return true
		}
		if strings.Contains(strings.ToLower(country.IsoAlpha3), strings.ToLower(req.QueryText)) {
			return true
		}
		return false
	}).([]models.Country)

	offset := 0
	limit := 500

	if offset < 0 {
		offset = 0
	}
	if limit < 0 {
		limit = 0
	}
	if offset > len(countries) {
		offset = len(countries)
	}
	if offset+limit > len(countries) {
		limit = len(countries) - offset
	}

	// Perform the slicing
	countries = countries[offset : offset+limit]

	response := gin.H{
		"data":    countries,
		"success": true,
	}

	// Cache the response
	setCachedResponse(c, cacheKey, response)

	c.JSON(http.StatusOK, response)
}

type airlineReq struct {
	QueryFields []string `form:"query_fields"`
	QueryText   string   `form:"query_text"`
	Limit       int      `form:"limit"`
	Offset      int      `form:"offset"`
}

type airlineResponse struct {
	Data    []datatypes.JSON `json:"data"`
	Limit   uint64           `json:"limit"`
	Offset  uint64           `json:"offset"`
	Total   uint64           `json:"total"`
	Success bool             `json:"success"`
}

// GetAirlineList get airline list
func GetAirlineList(c *gin.Context) {
	var (
		req airlineReq
		err error
	)

	if err = c.ShouldBindQuery(&req); err != nil {
		response.HandleResponse(c, nil, err)
		return
	}
	if req.Limit <= 0 {
		req.Limit = 200
	}

	// Generate cache key based on request parameters
	cacheKey := generateCacheKey("airline_list", req)

	// Try to get cached response
	if cachedData, found := getCachedResponse(c, cacheKey); found {
		c.JSON(http.StatusOK, cachedData)
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	airportsRaw, _ := dao.DB().GetMasterDataValueByName("airline")

	airlines := []models.Airline{}
	json.Unmarshal(airportsRaw, &airlines)

	if len(req.QueryFields) == 0 {
		req.QueryFields = []string{"name", "icao"}
	}

	if req.QueryText != "" {
		airlines = funk.Filter(airlines, func(airport models.Airline) bool {
			if funk.ContainsString(req.QueryFields, "name") && strings.Contains(strings.ToLower(airport.Name), strings.ToLower(req.QueryText)) {
				return true
			} else if funk.ContainsString(req.QueryFields, "icao") && strings.Contains(strings.ToLower(airport.Icao), strings.ToLower(req.QueryText)) {
				return true
			}
			return false
		}).([]models.Airline)

		sortAirlines(airlines, req.QueryText)
	}
	offset := req.Offset
	limit := req.Limit

	if offset < 0 {
		offset = 0
	}
	if limit < 0 {
		limit = 0
	}
	if offset > len(airlines) {
		offset = len(airlines)
	}
	if offset+limit > len(airlines) {
		limit = len(airlines) - offset
	}

	// Perform the slicing
	airlines = airlines[offset : offset+limit]

	response := gin.H{
		"data":    airlines,
		"success": true,
	}

	// Cache the response
	setCachedResponse(c, cacheKey, response)

	c.JSON(http.StatusOK, response)
}

type AirportReq struct {
	QueryFields []string `form:"query_fields"`
	QueryText   string   `form:"query_text"`
	Iata        []string `form:"iata"`
	CountryCode []string `form:"country_code"`
	Limit       int      `form:"limit"`
	Offset      int      `form:"offset"`
}

type AirportResponse struct {
	Data    any   `json:"data"`
	Limit   int   `json:"limit"`
	Offset  int   `json:"offset"`
	Total   int64 `json:"total"`
	Success bool  `json:"success"`
}

// GetAirportList get airport list
func GetAirportList(c *gin.Context) {
	var req AirportReq
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if req.Limit <= 0 {
		req.Limit = 100
	}

	// Generate cache key based on request parameters
	cacheKey := generateCacheKey("airport_list", req)

	// Try to get cached response
	if cachedData, found := getCachedResponse(c, cacheKey); found {
		c.JSON(http.StatusOK, cachedData)
		return
	}

	dao, err := middlewares.GetVisaDao(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	airportsRaw, _ := dao.DB().GetMasterDataValueByName("airport")

	airports := []models.Airport{}
	json.Unmarshal(airportsRaw, &airports)
	compareString := func(list []string, item string) bool {
		if item == "" {
			return false
		}
		for _, i := range list {
			if strings.Contains(strings.ToLower(i), strings.ToLower(item)) {
				return true
			}
		}
		return false
	}

	if len(req.Iata) > 0 {
		airports = funk.Filter(airports, func(airport models.Airport) bool {
			return compareString(req.Iata, airport.Iata)
		}).([]models.Airport)
	}

	if len(req.CountryCode) > 0 {
		airports = funk.Filter(airports, func(airport models.Airport) bool {
			return compareString(req.CountryCode, airport.CountryCode)
		}).([]models.Airport)
	}

	if len(req.QueryFields) > 0 && req.QueryText != "" {
		airports = funk.Filter(airports, func(airport models.Airport) bool {
			return (funk.ContainsString(req.QueryFields, "iata") && compareString([]string{airport.Iata}, req.QueryText)) ||
				(funk.ContainsString(req.QueryFields, "name") && compareString([]string{airport.Name}, req.QueryText)) ||
				(funk.ContainsString(req.QueryFields, "city") && compareString([]string{airport.City}, req.QueryText)) ||
				(funk.ContainsString(req.QueryFields, "country_code") && compareString([]string{airport.CountryCode}, req.QueryText))
		}).([]models.Airport)

		sortAirports(airports, req.QueryText)

	}

	offset := req.Offset
	limit := req.Limit

	if offset < 0 {
		offset = 0
	}
	if limit < 0 {
		limit = 0
	}
	if offset > len(airports) {
		offset = len(airports)
	}
	if offset+limit > len(airports) {
		limit = len(airports) - offset
	}

	// Perform the slicing
	airports = airports[offset : offset+limit]

	response := AirportResponse{
		Data:    airports,
		Limit:   req.Limit,
		Offset:  req.Offset,
		Success: true,
	}

	// Cache the response
	setCachedResponse(c, cacheKey, response)

	c.JSON(http.StatusOK, response)
}

func sortAirports(airports []models.Airport, queryText string) {
	sort.Slice(airports, func(i, j int) bool {
		containsI := strings.Contains(strings.ToLower(airports[i].Iata), strings.ToLower(queryText))
		containsJ := strings.Contains(strings.ToLower(airports[j].Iata), strings.ToLower(queryText))

		if containsI && containsJ {
			return airports[i].Name < airports[j].Name
		}

		if containsI {
			return true
		}

		if containsJ {
			return false
		}

		return airports[i].Name < airports[j].Name
	})
}

func sortAirlines(airlines []models.Airline, queryText string) {
	sort.Slice(airlines, func(i, j int) bool {
		containsI := strings.Contains(strings.ToLower(airlines[i].Iata), strings.ToLower(queryText))
		containsJ := strings.Contains(strings.ToLower(airlines[j].Iata), strings.ToLower(queryText))

		if containsI && containsJ {
			return airlines[i].Name < airlines[j].Name
		}

		if containsI {
			return true
		}

		if containsJ {
			return false
		}

		return airlines[i].Name < airlines[j].Name
	})
}

// Cache utility functions
const (
	cacheTTL = 3600 // 1 hour in seconds
)

// generateCacheKey creates a unique cache key based on the request parameters
func generateCacheKey(prefix string, params interface{}) string {
	data, _ := json.Marshal(params)
	hash := md5.Sum(data)
	return fmt.Sprintf("%s:%s", prefix, hex.EncodeToString(hash[:]))
}

// getCachedResponse retrieves cached response from Redis
func getCachedResponse(c *gin.Context, cacheKey string) (interface{}, bool) {
	rdb := middlewares.GetRedis(c)
	if rdb == nil {
		return nil, false
	}

	ctx := context.Background()
	val, err := rdb.Get(ctx, cacheKey).Result()
	if err != nil {
		return nil, false
	}

	var result interface{}
	if err := json.Unmarshal([]byte(val), &result); err != nil {
		return nil, false
	}

	return result, true
}

// setCachedResponse stores response in Redis cache
func setCachedResponse(c *gin.Context, cacheKey string, data interface{}) {
	rdb := middlewares.GetRedis(c)
	if rdb == nil {
		return
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return
	}

	ctx := context.Background()
	rdb.Set(ctx, cacheKey, jsonData, time.Duration(cacheTTL)*time.Second)
}
