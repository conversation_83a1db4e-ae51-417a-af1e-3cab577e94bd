package middlewares

import (
	"context"
	"log"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

func SetRedis(address string) gin.HandlerFunc {
	return func(c *gin.Context) {
		rdb := redis.NewClient(&redis.Options{
			Addr:         "*************:6379",
			Password:     "8dc2835978f2c981",
			DB:           2,
			DialTimeout:  5 * time.Second,
			ReadTimeout:  3 * time.Second,
			WriteTimeout: 3 * time.Second,
			PoolTimeout:  4 * time.Second,
		})

		// Test Redis connection with timeout - don't fail if Redis is unavailable
		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		defer cancel()

		_, err := rdb.Ping(ctx).Result()
		if err != nil {
			// Log warning but don't crash the application
			log.Printf("Warning: Redis connection failed, caching will be disabled: %v", err)
			// Set nil to indicate Redis is unavailable
			c.Set("redis", nil)
		} else {
			c.Set("redis", rdb)
		}

		c.Next()
	}
}

func GetRedis(c *gin.Context) *redis.Client {
	v := c.Value("redis")
	if v == nil {
		return nil
	}
	dao, ok := v.(*redis.Client)
	if !ok {
		return nil
	}
	return dao
}
