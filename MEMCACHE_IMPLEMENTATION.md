# Memcache Implementation for Master Data APIs

## Overview

This implementation adds Redis-based caching (memcache) to the master data APIs in the country handler to return cached responses for identical requests, improving performance and reducing database load.

## APIs Enhanced with Caching

1. **GetCountryList** - `/v1/master-data/public/countries`
2. **GetAirlineList** - `/v1/master-data/public/airlines`  
3. **GetAirportList** - `/v1/master-data/public/airports`

## Implementation Details

### Cache Strategy
- **Cache-Aside Pattern**: Check cache first, if miss then fetch from database and populate cache
- **TTL**: 1 hour (3600 seconds) for all master data responses
- **Key Generation**: MD5 hash of request parameters with API-specific prefix

### Cache Key Format
```
{prefix}:{md5_hash_of_request_params}
```

Examples:
- `country_list:a1b2c3d4e5f6...`
- `airline_list:f6e5d4c3b2a1...`
- `airport_list:1a2b3c4d5e6f...`

### Files Modified

#### 1. `golang_services/handlers/master_data/v1/country_handler.go`
- Added cache utility functions:
  - `generateCacheKey()` - Creates unique cache keys from request parameters
  - `getCachedResponse()` - Retrieves cached responses from Redis
  - `setCachedResponse()` - Stores responses in Redis cache
- Enhanced all three handler functions with caching logic
- Added required imports: `context`, `crypto/md5`, `encoding/hex`, `fmt`, `time`

#### 2. `golang_services/master_data/server.go`
- Added Redis middleware to the server setup: `middlewares.SetRedis("")`

#### 3. `golang_services/handlers/master_data/v1/country_handler_test.go` (New)
- Comprehensive test suite for cache functionality
- Tests cache key generation, uniqueness, and consistency

## Cache Behavior

### Cache Hit
1. Request comes in
2. Generate cache key from request parameters
3. Check Redis for existing cached response
4. If found, return cached response immediately
5. No database query needed

### Cache Miss
1. Request comes in
2. Generate cache key from request parameters
3. Check Redis - no cached response found
4. Execute normal database query and processing
5. Store response in Redis with TTL
6. Return response to client

### Cache Key Uniqueness
Different request parameters generate different cache keys:
- Different query text: `vietnam` vs `thailand`
- Different query fields: `["name"]` vs `["iata"]`
- Different pagination: `limit=100` vs `limit=200`
- Different filters: `country_code=["US"]` vs `country_code=["VN"]`

## Configuration

### Redis Connection
The Redis middleware uses hardcoded connection settings:
- **Host**: `**************:6379`
- **Password**: `AD@Redis`
- **Database**: 0

### Cache TTL
- **Duration**: 1 hour (3600 seconds)
- **Configurable**: Can be modified in the `cacheTTL` constant

## Error Handling

- **Redis Unavailable**: APIs continue to work normally without caching
- **Cache Errors**: Silently ignored, fallback to database queries
- **Serialization Errors**: Gracefully handled, no cache storage

## Performance Benefits

1. **Reduced Database Load**: Identical requests served from cache
2. **Faster Response Times**: Redis lookup vs database query + processing
3. **Scalability**: Better handling of high-traffic scenarios
4. **Resource Efficiency**: Less CPU and memory usage for repeated requests

## Testing

Run the test suite:
```bash
cd golang_services/handlers/master_data/v1
go test -v
```

Tests cover:
- Cache key generation and uniqueness
- Parameter variation handling
- Consistency across multiple calls
- Graceful degradation when Redis is unavailable

## Monitoring

To monitor cache effectiveness:
1. Check Redis for cache hit/miss ratios
2. Monitor API response times before/after implementation
3. Observe database query reduction

## Future Enhancements

1. **Cache Invalidation**: Implement cache clearing when master data updates
2. **Metrics**: Add cache hit/miss metrics and monitoring
3. **Configuration**: Make Redis connection and TTL configurable via environment variables
4. **Compression**: Add response compression for large datasets
5. **Distributed Caching**: Consider Redis Cluster for high availability
