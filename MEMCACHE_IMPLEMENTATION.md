# Memcache Implementation for Master Data APIs

## Overview

This implementation adds Redis-based caching (memcache) to the master data APIs in the country handler to cache raw database results from `dao.DB().GetMasterDataValueByName()` calls, improving performance and reducing database load.

## APIs Enhanced with Caching

1. **GetCountryList** - `/v1/master-data/public/countries`
2. **GetAirlineList** - `/v1/master-data/public/airlines`
3. **GetAirportList** - `/v1/master-data/public/airports`

## Implementation Details

### Cache Strategy
- **Cache-Aside Pattern**: Check cache first for raw master data, if miss then fetch from database and populate cache
- **TTL**: 1 hour (3600 seconds) for raw master data
- **Scope**: Only caches the raw JSON data from `GetMasterDataValueByName()`, not the processed/filtered results

### Cache Key Format
```
master_data:{data_name}
```

Examples:
- `master_data:country`
- `master_data:airline`
- `master_data:airport`

### Files Modified

#### 1. `golang_services/handlers/master_data/v1/country_handler.go`
- Added cache utility function:
  - `getCachedMasterData()` - Retrieves cached raw master data or fetches from database if not cached
- Enhanced all three handler functions to use cached master data
- Replaced direct `dao.DB().GetMasterDataValueByName()` calls with `getCachedMasterData()`
- Added required imports: `context`, `crypto/md5`, `encoding/hex`, `fmt`, `time`

#### 2. `golang_services/master_data/server.go`
- Added Redis middleware to the server setup: `middlewares.SetRedis("")`

#### 3. `golang_services/handlers/master_data/v1/country_handler_test.go` (New)
- Comprehensive test suite for cache functionality
- Tests cache key generation, uniqueness, and consistency

## Cache Behavior

### Cache Hit
1. Request comes in with query parameters
2. Generate cache key for master data type (e.g., `master_data:country`)
3. Check Redis for existing cached raw data
4. If found, use cached raw data for processing
5. Apply filtering, sorting, and pagination to cached data
6. Return processed results to client

### Cache Miss
1. Request comes in with query parameters
2. Generate cache key for master data type
3. Check Redis - no cached raw data found
4. Execute database query: `dao.DB().GetMasterDataValueByName(dataName)`
5. Store raw data in Redis with TTL
6. Apply filtering, sorting, and pagination to raw data
7. Return processed results to client

### Cache Efficiency
- **Same raw data**: All requests for the same data type (country/airline/airport) use the same cached raw data
- **Different processing**: Each request can have different query parameters, but they all work with the same cached raw data
- **Optimal performance**: Database is only hit once per data type per TTL period

## Configuration

### Redis Connection
The Redis middleware uses hardcoded connection settings:
- **Host**: `**************:6379`
- **Password**: `AD@Redis`
- **Database**: 0

### Cache TTL
- **Duration**: 1 hour (3600 seconds)
- **Configurable**: Can be modified in the `masterDataCacheTTL` constant

## Error Handling

- **Redis Unavailable**: APIs continue to work normally without caching
- **Cache Errors**: Silently ignored, fallback to database queries
- **Serialization Errors**: Gracefully handled, no cache storage

## Performance Benefits

1. **Reduced Database Load**: Raw master data fetched only once per TTL period per data type
2. **Faster Response Times**: Redis lookup vs database query for raw data
3. **Scalability**: Better handling of high-traffic scenarios with multiple different query parameters
4. **Resource Efficiency**: Database queries reduced significantly, processing still happens per request
5. **Optimal Caching**: Caches the most reusable part (raw data) while allowing dynamic filtering/sorting

## Testing

Run the test suite:
```bash
cd golang_services/handlers/master_data/v1
go test -v
```

Tests cover:
- Master data cache key generation and format
- Cache key consistency across multiple calls
- Graceful degradation when Redis is unavailable
- Cache behavior verification

## Monitoring

To monitor cache effectiveness:
1. Check Redis for cache hit/miss ratios
2. Monitor API response times before/after implementation
3. Observe database query reduction

## Future Enhancements

1. **Cache Invalidation**: Implement cache clearing when master data updates
2. **Metrics**: Add cache hit/miss metrics and monitoring
3. **Configuration**: Make Redis connection and TTL configurable via environment variables
4. **Compression**: Add response compression for large datasets
5. **Distributed Caching**: Consider Redis Cluster for high availability
